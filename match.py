
import pandas as pd
print("pandas 1")
import numpy as np
print("pandas 2")
import time
print("pandas 3")
import os
print("pandas 4")
from tqdm import tqdm
print("pandas 5")
from sentence_transformers import SentenceTransformer
print("pandas 6")
import faiss  # Facebook AI Similarity Search - a free alternative to Pinecone
print("pandas 7")
def load_onbuy_categories(reference_file):
    """Load all OnBuy categories from Excel file"""
    all_categories = []
    
    # Try to read all sheets (Sheet2 to Sheet17)
    for sheet_num in range(2, 18):
        sheet_name = f'Sheet{sheet_num}'
        try:
            df = pd.read_excel(reference_file, sheet_name=sheet_name)
            
            # Try to find the category column
            category_col = None
            for col in df.columns:
                if 'onbuy category' in col.lower() or 'category' in col.lower() or 'idonbuy' in col.lower():
                    category_col = col
                    break
                    
            if category_col:
                # Extract categories
                categories = df[category_col].astype(str).tolist()
                categories = [cat for cat in categories if cat and cat.lower() != 'nan']
                all_categories.extend(categories)
                print(f"Loaded {len(categories)} categories from {sheet_name}")
        except Exception as e:
            print(f"Error reading sheet {sheet_name}: {e}")
    
    return all_categories

def create_embedding_index(categories):
    """Create embeddings using Sentence Transformers and build a FAISS index"""
    print("Loading embedding model...")
    # Use a pre-trained model - this will download once on first run
    model = SentenceTransformer('all-MiniLM-L6-v2')  # Small, fast model (~80MB)
    # For better quality: 'all-mpnet-base-v2' (~420MB)
    
    print(f"Creating embeddings for {len(categories)} categories...")
    # Generate embeddings for all categories
    # embeddings = model.encode(categories, show_progress_bar=True)
    try:
        embeddings = model.encode(categories, show_progress_bar=True, batch_size=16)
    except Exception as e:
        print(f"Error during embedding creation: {e}")

    # Get embedding dimension
    dimension = embeddings.shape[1]
    
    # Create FAISS index (much faster than doing individual similarity calculations)
    print("Building FAISS index...")
    index = faiss.IndexFlatL2(dimension)  # L2 distance
    faiss.normalize_L2(embeddings)  # Normalize vectors for cosine similarity
    index.add(embeddings)
    
    return model, index, categories

def match_amazon_category(amazon_category, model, faiss_index, all_categories, top_k=1):
    """Find the best matching OnBuy category for an Amazon category"""
    try:
        # Create embedding for Amazon category
        query_embedding = model.encode([amazon_category])
        faiss.normalize_L2(query_embedding)
        
        # Search FAISS index
        distances, indices = faiss_index.search(query_embedding, top_k)
        
        # Process results
        results = []
        for i in range(len(indices[0])):
            idx = indices[0][i]
            distance = distances[0][i]
            # Convert L2 distance to similarity score (1 is perfect match, 0 is no match)
            similarity = 1 - min(distance, 2.0) / 2.0
            results.append((all_categories[idx], similarity))
        
        if results:
            return results[0][0], results[0][1]
        else:
            return "No match found", 0.0
            
    except Exception as e:
        print(f"Error matching '{amazon_category}': {e}")
        return f"Error: {str(e)}", 0.0

def process_sample_file(sample_file, model, faiss_index, all_categories, output_file=None):
    """Process sample data file and match categories"""
    if output_file is None:
        output_file = 'Imran_Sample_Data_Matched_Embeddings.xlsx'
    
    # Load sample data
    sample_data = pd.read_excel(sample_file)
    
    # Find category column
    if 'Categories' not in sample_data.columns:
        potential_cols = [col for col in sample_data.columns if 'categ' in col.lower()]
        if potential_cols:
            print(f"'Categories' column not found. Using '{potential_cols[0]}' instead.")
            sample_data.rename(columns={potential_cols[0]: 'Categories'}, inplace=True)
        else:
            raise ValueError("'Categories' column not found in sample data file")
    
    # Process each row
    matched_categories = []
    match_scores = []
    
    print("Matching categories...")
    for idx, row in enumerate(tqdm(sample_data.iterrows(), total=len(sample_data))):
        _, row_data = row
        amazon_category = str(row_data['Categories'])
        
        if amazon_category.lower() == 'nan' or not amazon_category:
            matched_categories.append("No category provided")
            match_scores.append(0.0)
            continue
        
        # Match category
        matched_category, score = match_amazon_category(amazon_category, model, faiss_index, all_categories)
        matched_categories.append(matched_category)
        match_scores.append(score)
    
    # Add results to sample data
    sample_data['Matched OnBuy Category'] = matched_categories
    sample_data['Match Score'] = match_scores
    
    # Add root category (extract from matched category)
    root_categories = []
    for cat in matched_categories:
        if '>' in cat:
            root_categories.append(cat.split('>')[0].strip())
        else:
            root_categories.append("")
    
    sample_data['Matched OnBuy Root'] = root_categories
    
    # Save results
    sample_data.to_excel(output_file, index=False)
    print(f"Results saved to '{output_file}'")

def main():
    print("starting .....")
    print("=== Amazon to OnBuy Category Matcher using Embeddings ===")
    
    
    # Get input files
    reference_file = input("Enter path to OnBuy Categories Excel file (default: 'R1.xlsx'): ") or 'R1.xlsx'
    sample_file = input("Enter path to Sample Data Excel file (default: 'Imran Sample Data.xlsx'): ") or 'Imran Sample Data.xlsx'
    output_file = input("Enter path for output file (default: 'Matching_03-05-2025.xlsx'): ") or 'Matching_03-05-2025.xlsx'
    
    # Load categories
    print("Loading OnBuy categories...")
    categories = load_onbuy_categories(reference_file)
    
    # Create embeddings and build index
    model, faiss_index, categories = create_embedding_index(categories)
    
    # Process sample file
    process_sample_file(sample_file, model, faiss_index, categories, output_file)
    
    print("Process complete!")

if __name__ == "__main__":
    print("=== Running as __main__ ===")
    main()